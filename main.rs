//! ExtensibleDB – An extensible, embedded database engine written in Rust.
//!
//! Goals:
//! • Extensible like PostgreSQL (runtime-loadable plugins / user-defined functions).
//! • Portable like SQLite (single-file, zero-config storage powered by `sled`).

use anyhow::{Context, Result};
use mlua::{Error as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, LuaSerdeExt, UserData, UserDataMethods, Value as LuaValue};
use serde_json::Value;
use sled::Db;
use std::io::{self, Write};
use std::path::Path;

#[derive(Clone)]
pub struct Database {
    storage: Db,
}

// Expose the database to Lua.
impl UserData for Database {
    fn add_methods<'lua, M: UserDataMethods<'lua, Self>>(methods: &mut M) {
        methods.add_method("create_table", |_, this, name: String| {
            this.create_table(&name).map_err(LuaError::external)?;
            Ok(())
        });

        methods.add_method(
            "insert_row",
            |lua, this, (table, row): (String, LuaValue)| {
                let json_row: Value = lua.from_value(row)?;
                this.insert_row(&table, json_row)
                    .map_err(LuaError::external)?;
                Ok(())
            },
        );

        methods.add_method("select_all", |lua, this, table: String| {
            let rows = this.select_all(&table).map_err(LuaError::external)?;
            lua.to_value(&rows)
        });
    }
}
fn main() -> Result<()> {
    let db = Database::open("data.db")?;
    let lua = Lua::new();
    lua.globals().set("db", db)?;

    println!("ExtensibleDB Lua console (type .exit to quit)");
    let stdin = io::stdin();

    loop {
        print!("lua> ");
        io::stdout().flush()?;

        let mut line = String::new();
        stdin.read_line(&mut line)?;
        let cmd = line.trim();

        if cmd.is_empty() {
            continue;
        }
        if cmd == ".exit" {
            break;
        }

        match lua.load(cmd).eval::<LuaValue>() {
            Ok(val) => {
                if !matches!(val, LuaValue::Nil) {
                    println!("{:?}", val);
                }
            }
            Err(e) => eprintln!("error: {e}"),
        }
    }
    Ok(())
}

// Database method implementations
impl Database {
    /// Open (or create) a database file at the provided path.
    pub fn open<P: AsRef<Path>>(path: P) -> Result<Self> {
        let storage = sled::open(path).context("failed to open storage")?;
        Ok(Self { storage })
    }

    /// Execute one or multiple SQL statements.
    pub fn execute(&self, _sql: &str) -> Result<()> {
        // -------------------------
        // New non-SQL API (unimplemented)
        // -------------------------
        // TODO: implement SQL execution
        Ok(())
    }
    /// Create a new table (sled tree) if it doesn't already exist.
    pub fn create_table(&self, name: &str) -> Result<()> {
        self.storage.open_tree(name)?;
        Ok(())
    }

    /// Insert a row represented as JSON into the given table.
    pub fn insert_row(&self, table: &str, row: Value) -> Result<()> {
        let tree = self.storage.open_tree(table)?;
        let key = self.storage.generate_id()?.to_be_bytes().to_vec();
        let data = serde_json::to_vec(&row)?;
        tree.insert(key, data)?;
        Ok(())
    }

    /// Fetch all rows from a table, returned as Vec<serde_json::Value>.
    pub fn select_all(&self, table: &str) -> Result<Vec<Value>> {
        let tree = self.storage.open_tree(table)?;
        let mut rows = Vec::new();
        for kv in tree.iter() {
            let (_k, v) = kv?;
            let row: Value = serde_json::from_slice(&v)?;
            rows.push(row);
        }
        Ok(rows)
    }
} // impl Database
