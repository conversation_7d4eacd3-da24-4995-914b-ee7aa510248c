[package]
name = "extensibledb"
version = "0.1.0"
edition = "2021"

description = "An extensible embedded database engine inspired by PostgreSQL and SQLite"
license = "MIT OR Apache-2.0"
repository = "https://example.com/extensibledb"

[dependencies]
anyhow = "1"
sled = "0.34"
mlua = { version = "0.8", features = ["lua54", "send", "serialize"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
parking_lot = "0.12"

[profile.release]
lto = true
codegen-units = 1
opt-level = "z"

[[bin]]
name = "extensibledb"
path = "main.rs"
