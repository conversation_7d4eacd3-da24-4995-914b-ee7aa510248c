# ExtensibleDB

An extensible, embedded database engine written in Rust that combines the portability of SQLite with the extensibility of PostgreSQL through Lua scripting.

## Features

- **Embedded Storage**: Single-file, zero-config database powered by [Sled](https://github.com/spacejam/sled)
- **Lua Extensibility**: Runtime scripting and custom functions via [MLua](https://github.com/khvzak/mlua)
- **JSON Documents**: Flexible schema-less data storage with JSON
- **Interactive Console**: Built-in Lua REPL for database operations

## Installation

Make sure you have Rust installed, then clone and build:

```bash
git clone <repository-url>
cd extensibledb
cargo build --release
```

## Quick Start

Run the interactive Lua console:

```bash
cargo run
```

You'll see:
```
ExtensibleDB Lua console (type .exit to quit)
lua> 
```

## Programming with Lua

The database is exposed as a global `db` object in the Lua environment with the following methods:

### Creating Tables

```lua
-- Create a new table (sled tree)
db:create_table("users")
db:create_table("posts")
```

### Inserting Data

```lua
-- Insert JSON documents
db:insert_row("users", {
    name = "Alice",
    email = "<EMAIL>",
    age = 30
})

db:insert_row("users", {
    name = "Bob", 
    email = "<EMAIL>",
    age = 25,
    active = true
})

db:insert_row("posts", {
    title = "Hello World",
    content = "This is my first post!",
    author = "Alice",
    tags = {"intro", "hello"}
})
```

### Querying Data

```lua
-- Get all rows from a table
local users = db:select_all("users")

-- Iterate through results
for i, user in ipairs(users) do
    print("User " .. i .. ": " .. user.name .. " (" .. user.email .. ")")
end

-- Access specific fields
local posts = db:select_all("posts")
for _, post in ipairs(posts) do
    print("Title: " .. post.title)
    print("Author: " .. post.author)
end
```

### Example Session

```lua
lua> db:create_table("inventory")
lua> db:insert_row("inventory", {item = "laptop", price = 999.99, stock = 5})
lua> db:insert_row("inventory", {item = "mouse", price = 29.99, stock = 50})
lua> items = db:select_all("inventory")
lua> for _, item in ipairs(items) do print(item.item .. ": $" .. item.price) end
laptop: $999.99
mouse: $29.99
```

## Advanced Lua Programming

### Data Processing

```lua
-- Calculate total inventory value
local inventory = db:select_all("inventory")
local total_value = 0

for _, item in ipairs(inventory) do
    total_value = total_value + (item.price * item.stock)
end

print("Total inventory value: $" .. total_value)
```

### Filtering Data

```lua
-- Find expensive items (>$100)
local inventory = db:select_all("inventory")
local expensive_items = {}

for _, item in ipairs(inventory) do
    if item.price > 100 then
        table.insert(expensive_items, item)
    end
end

print("Found " .. #expensive_items .. " expensive items")
```

### Custom Functions

```lua
-- Define a helper function
function add_user(name, email, age)
    db:insert_row("users", {
        name = name,
        email = email,
        age = age,
        created_at = os.time()
    })
    print("Added user: " .. name)
end

-- Use the function
add_user("Charlie", "<EMAIL>", 28)
```

## Data Types

ExtensibleDB stores data as JSON, supporting:

- **Strings**: `"hello world"`
- **Numbers**: `42`, `3.14159`
- **Booleans**: `true`, `false`
- **Arrays**: `{1, 2, 3}`, `{"a", "b", "c"}`
- **Objects**: `{name = "Alice", age = 30}`
- **Nested structures**: `{user = {name = "Bob", settings = {theme = "dark"}}}`

## Console Commands

- **`.exit`**: Quit the console
- Any valid Lua expression or statement

## File Structure

- **`data.db/`**: Database directory (created automatically)
- Contains Sled trees (one per table)

## API Reference

### Database Methods

| Method | Parameters | Description |
|--------|------------|-------------|
| `create_table(name)` | `name: string` | Create a new table |
| `insert_row(table, data)` | `table: string, data: table` | Insert JSON document |
| `select_all(table)` | `table: string` | Get all rows from table |

### Return Values

- `create_table()`: No return value
- `insert_row()`: No return value  
- `select_all()`: Array of JSON objects

## Error Handling

```lua
-- Lua will show errors for invalid operations
lua> db:select_all("nonexistent_table")
error: external error: failed to open storage: ...
```

## Building from Source

```bash
# Debug build
cargo build

# Release build (optimized)
cargo build --release

# Run tests
cargo test
```

## Dependencies

- **Rust 2021 Edition**
- **Sled**: Embedded database engine
- **MLua**: Lua integration
- **Serde**: JSON serialization
- **Anyhow**: Error handling

## License

MIT OR Apache-2.0

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Roadmap

- [ ] SQL query support
- [ ] Indexing and search
- [ ] Transactions
- [ ] Schema validation
- [ ] Plugin system
- [ ] Network interface
- [ ] Backup/restore utilities

---

**Happy coding with ExtensibleDB!** 🚀
