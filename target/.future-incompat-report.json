{"version": 0, "next_id": 2, "reports": [{"id": 1, "suggestion_message": "\nTo solve this problem, you can try the following approaches:\n\n\n- Some affected dependencies have newer versions available.\nYou may want to consider updating them to a newer version to see if the issue has been fixed.\n\nmlua v0.8.10 has the following newer versions available: 0.9.0-beta.1, 0.9.0-beta.2, 0.9.0-beta.3, 0.9.0-rc.1, 0.9.0-rc.2, 0.9.0-rc.3, 0.9.0, 0.9.1, 0.9.2, 0.9.3, 0.9.4, 0.9.5, 0.9.6, 0.9.7, 0.9.8, 0.9.9, 0.10.0-beta.1, 0.10.0-beta.2, 0.10.0-rc.1, 0.10.0, 0.10.1, 0.10.2, 0.10.3, 0.10.5, 0.11.0-beta.1, 0.11.0-beta.2, 0.11.0-beta.3, 0.11.0, 0.11.1, 0.11.2\n\n- If the issue is not solved by updating the dependencies, a fix has to be\nimplemented by those dependencies. You can help with that by notifying the\nmaintainers of this problem (e.g. by creating a bug report) or by proposing a\nfix to the maintainers (e.g. by creating a pull request):\n\n  - mlua@0.8.10\n  - Repository: https://github.com/khvzak/mlua\n  - Detailed warning command: `cargo report future-incompatibilities --id 1 --package mlua@0.8.10`\n\n- If waiting for an upstream fix is not an option, you can use the `[patch]`\nsection in `Cargo.toml` to use your own version of the dependency. For more\ninformation, see:\nhttps://doc.rust-lang.org/cargo/reference/overriding-dependencies.html#the-patch-section\n", "per_package": {"mlua@0.8.10": "The package `mlua v0.8.10` currently triggers the following future incompatibility lints:\n> \u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mlua-0.8.10/src/chunk.rs:289:5\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn exec(self) -> Result<()> {\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: UserData` will fail\u001b[0m\n> \u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mlua-0.8.10/src/chunk.rs:290:14\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.call(())?;\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n> \u001b[0m\u001b[1m\u001b[38;5;12m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        self.call\u001b[0m\u001b[0m\u001b[38;5;10m::<_, ()>\u001b[0m\u001b[0m(())?;\u001b[0m\n> \u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++\u001b[0m\n> \n"}}]}