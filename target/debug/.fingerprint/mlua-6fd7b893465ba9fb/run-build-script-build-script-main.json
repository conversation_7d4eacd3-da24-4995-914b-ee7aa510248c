{"rustc": 12255178119460964245, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8425953583706058976, "build_script_main", false, 5631824094641880096]], "local": [{"RerunIfChanged": {"output": "debug/build/mlua-6fd7b893465ba9fb/output", "paths": ["build"]}}, {"RerunIfEnvChanged": {"var": "LUA_INC", "val": null}}, {"RerunIfEnvChanged": {"var": "LUA_LIB", "val": null}}, {"RerunIfEnvChanged": {"var": "LUA_LIB_NAME", "val": null}}, {"RerunIfEnvChanged": {"var": "LUA_LINK", "val": null}}, {"RerunIfEnvChanged": {"var": "LUA_NO_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "LUA_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LUA_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "SYSROOT", "val": null}}, {"RerunIfEnvChanged": {"var": "LUA_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LUA_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "LUA_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LUA_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}