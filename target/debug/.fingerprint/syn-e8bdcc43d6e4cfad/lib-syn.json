{"rustc": 12255178119460964245, "features": "[\"clone-impls\", \"derive\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 4880908480588639907, "deps": [[373107762698212489, "proc_macro2", false, 5771864128153981377], [1988483478007900009, "unicode_ident", false, 2153903702800526199], [17990358020177143287, "quote", false, 4863092452727341988]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-e8bdcc43d6e4cfad/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}