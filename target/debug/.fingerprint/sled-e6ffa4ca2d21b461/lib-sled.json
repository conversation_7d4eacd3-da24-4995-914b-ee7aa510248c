{"rustc": 12255178119460964245, "features": "[\"default\", \"no_metrics\"]", "declared_features": "[\"backtrace\", \"color-backtrace\", \"compression\", \"default\", \"docs\", \"event_log\", \"failpoints\", \"io_uring\", \"lock_free_delays\", \"measure_allocs\", \"miri_optimizations\", \"mutex\", \"no_inline\", \"no_logs\", \"no_metrics\", \"pretty_backtrace\", \"rio\", \"testing\", \"zstd\"]", "target": 3855776470257171463, "profile": 2241668132362809309, "path": 14228302243009432939, "deps": [[3528074118530651198, "crossbeam_epoch", false, 5800395582410918496], [4468123440088164316, "crossbeam_utils", false, 12755176199796403124], [5986029879202738730, "log", false, 8017115278922149383], [7312356825837975969, "crc32fast", false, 12763362911022533164], [7521345276086848634, "fxhash", false, 17874811884412901372], [8128303468064674118, "fs2", false, 8742829155610416164], [11641406201058336332, "parking_lot", false, 7019510184667126107], [11887305395906501191, "libc", false, 7090976181968434492]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sled-e6ffa4ca2d21b461/dep-lib-sled", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}