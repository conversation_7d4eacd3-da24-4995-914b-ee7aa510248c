{"$message_type": "future_incompat", "future_incompat_report": [{"diagnostic": {"$message_type": "diagnostic", "message": "this function depends on never type fallback being `()`", "code": {"code": "dependency_on_unit_never_type_fallback", "explanation": null}, "level": "warning", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mlua-0.8.10/src/chunk.rs", "byte_start": 9175, "byte_end": 9206, "line_start": 289, "line_end": 289, "column_start": 5, "column_end": 36, "is_primary": true, "text": [{"text": "    pub fn exec(self) -> Result<()> {", "highlight_start": 5, "highlight_end": 36}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!", "code": null, "level": "warning", "spans": [], "children": [], "rendered": null}, {"message": "for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}, {"message": "specify the types explicitly", "code": null, "level": "help", "spans": [], "children": [], "rendered": null}, {"message": "in edition 2024, the requirement `!: UserData` will fail", "code": null, "level": "note", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mlua-0.8.10/src/chunk.rs", "byte_start": 9222, "byte_end": 9226, "line_start": 290, "line_end": 290, "column_start": 14, "column_end": 18, "is_primary": true, "text": [{"text": "        self.call(())?;", "highlight_start": 14, "highlight_end": 18}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [], "rendered": null}, {"message": "use `()` annotations to avoid fallback changes", "code": null, "level": "help", "spans": [{"file_name": "/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mlua-0.8.10/src/chunk.rs", "byte_start": 9226, "byte_end": 9226, "line_start": 290, "line_end": 290, "column_start": 18, "column_end": 18, "is_primary": true, "text": [{"text": "        self.call(())?;", "highlight_start": 18, "highlight_end": 18}], "label": null, "suggested_replacement": "::<_, ()>", "suggestion_applicability": "MachineApplicable", "expansion": null}], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this function depends on never type fallback being `()`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mlua-0.8.10/src/chunk.rs:289:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn exec(self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mwarning\u001b[0m\u001b[0m: this was previously accepted by the compiler but is being phased out; it will become a hard error in Rust 2024 and in a future release in all editions!\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/never-type-fallback.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: specify the types explicitly\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: in edition 2024, the requirement `!: UserData` will fail\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/mlua-0.8.10/src/chunk.rs:290:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        self.call(())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `()` annotations to avoid fallback changes\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        self.call\u001b[0m\u001b[0m\u001b[38;5;10m::<_, ()>\u001b[0m\u001b[0m(())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++\u001b[0m\n\n"}}]}