{"rustc": 12255178119460964245, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\", \"stdweb\", \"wasm-bindgen\"]", "target": 14160162848842265298, "profile": 2241668132362809309, "path": 13550991742932703957, "deps": [[8081351675046095464, "lock_api", false, 9437990168518206847], [14196108479452351812, "instant", false, 2245131624810769516], [14814334185036658946, "parking_lot_core", false, 2693744630041862579]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/parking_lot-ff74d6f10dcebd37/dep-lib-parking_lot", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}